<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateUserSessionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_sessions', function (Blueprint $table) {
            // Add missing columns
            if (!Schema::hasColumn('user_sessions', 'user_agent')) {
                $table->text('user_agent')->nullable()->after('ip_address');
            }
            if (!Schema::hasColumn('user_sessions', 'device_type')) {
                $table->string('device_type')->nullable()->after('user_agent');
            }
            if (!Schema::hasColumn('user_sessions', 'region')) {
                $table->string('region')->nullable()->after('country');
            }
            if (!Schema::hasColumn('user_sessions', 'latitude')) {
                $table->decimal('latitude', 10, 8)->nullable()->after('city');
            }
            if (!Schema::hasColumn('user_sessions', 'longitude')) {
                $table->decimal('longitude', 11, 8)->nullable()->after('latitude');
            }
            if (!Schema::hasColumn('user_sessions', 'last_activity')) {
                $table->timestamp('last_activity')->nullable()->after('logout_time');
            }

            // Add indexes for better performance
            try {
                $table->index(['user_id', 'is_active']);
            } catch (\Exception $e) {
                // Index might already exist
            }
            try {
                $table->index('session_id');
            } catch (\Exception $e) {
                // Index might already exist
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_sessions', function (Blueprint $table) {
            // Remove added columns
            $table->dropColumn([
                'user_agent',
                'device_type',
                'region',
                'latitude',
                'longitude',
                'last_activity'
            ]);

            // Drop indexes
            try {
                $table->dropIndex(['user_id', 'is_active']);
            } catch (\Exception $e) {
                // Index might not exist
            }
            try {
                $table->dropIndex(['session_id']);
            } catch (\Exception $e) {
                // Index might not exist
            }
        });
    }
}
