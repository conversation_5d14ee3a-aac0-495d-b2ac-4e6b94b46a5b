<?php return array(
    'root' => array(
        'name' => 'laravel/laravel',
        'pretty_version' => 'dev-main',
        'version' => 'dev-main',
        'reference' => '095384197db7976f3a19e2e3869127c52866f4d9',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'appzcoder/crud-generator' => array(
            'pretty_version' => 'v3.3.1',
            'version' => '3.3.1.0',
            'reference' => '207dbdd1f9a8dd8c791d5304d57826c1bbb510b3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../appzcoder/crud-generator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'appzcoder/laravel-admin' => array(
            'pretty_version' => 'v3.3.5',
            'version' => '3.3.5.0',
            'reference' => '647ffc3d04626a08ddcd14f7bc218d9ccb251325',
            'type' => 'library',
            'install_path' => __DIR__ . '/../appzcoder/laravel-admin',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arcanedev/log-viewer' => array(
            'pretty_version' => '8.3.0',
            'version' => '8.3.0.0',
            'reference' => '8badbc9435d8d73add6f39ec03076e735db90350',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arcanedev/log-viewer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'arcanedev/support' => array(
            'pretty_version' => '8.1.0',
            'version' => '8.1.0.0',
            'reference' => 'b161c3c080b314e832410295011625721fbd3a2f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../arcanedev/support',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'astrotomic/laravel-translatable' => array(
            'pretty_version' => 'v11.12.1',
            'version' => '11.12.1.0',
            'reference' => '04de8d1a7c8299a4071fd6bede41d47d264f2d4d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../astrotomic/laravel-translatable',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'barryvdh/laravel-dompdf' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => 'c96f90c97666cebec154ca1ffb67afed372114d8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../barryvdh/laravel-dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'brick/math' => array(
            'pretty_version' => '0.11.0',
            'version' => '0.11.0.0',
            'reference' => '0ad82ce168c82ba30d1c01ec86116ab52f589478',
            'type' => 'library',
            'install_path' => __DIR__ . '/../brick/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cordoval/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'davedevelopment/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f41715465d65213d644d3141a6a93081be5d3549',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/inflector' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'f9301a5b2fb1216b2b08f02ba04dc45423db6bff',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/inflector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/instantiator' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => '0a0fa9780f5d4e507415a065172d26a98d02047b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/instantiator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '1.2.3',
            'version' => '1.2.3.0',
            'reference' => 'c268e882d4dbdd85e36e4ad69e02dc284f89d229',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v2.0.8',
            'version' => '2.0.8.0',
            'reference' => 'c20247574601700e1f7c8dab39310fca1964dc52',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dragonmantank/cron-expression' => array(
            'pretty_version' => 'v3.3.3',
            'version' => '3.3.3.0',
            'reference' => 'adfb1f505deb6384dc8b39804c5065dd3c8c8c0a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dragonmantank/cron-expression',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '2.1.25',
            'version' => '2.1.25.0',
            'reference' => '0dbf5d78455d4d6a41d186da50adc1122ec066f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'facade/flare-client-php' => array(
            'pretty_version' => '1.10.0',
            'version' => '1.10.0.0',
            'reference' => '213fa2c69e120bca4c51ba3e82ed1834ef3f41b8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../facade/flare-client-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'facade/ignition' => array(
            'pretty_version' => '2.17.7',
            'version' => '2.17.7.0',
            'reference' => 'b4f5955825bb4b74cba0f94001761c46335c33e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../facade/ignition',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'facade/ignition-contracts' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '3c921a1cdba35b68a7f0ccffc6dffc1995b18267',
            'type' => 'library',
            'install_path' => __DIR__ . '/../facade/ignition-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fideloper/proxy' => array(
            'pretty_version' => '4.4.2',
            'version' => '4.4.2.0',
            'reference' => 'a751f2bc86dd8e6cfef12dc0cbdada82f5a18750',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fideloper/proxy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'filp/whoops' => array(
            'pretty_version' => '2.15.3',
            'version' => '2.15.3.0',
            'reference' => 'c83e88a30524f9360b11f585f71e6b17313b7187',
            'type' => 'library',
            'install_path' => __DIR__ . '/../filp/whoops',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'firebase/php-jwt' => array(
            'pretty_version' => 'v6.10.1',
            'version' => '6.10.1.0',
            'reference' => '500501c2ce893c824c801da135d02661199f60c5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../firebase/php-jwt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'fzaninotto/faker' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '5ffe7db6c80f441f150fc88008d64e64af66634b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../fzaninotto/faker',
            'aliases' => array(
                0 => '1.9.x-dev',
            ),
            'dev_requirement' => true,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.1',
            'version' => '1.1.1.0',
            'reference' => '672eff8cf1d6fe1ef09ca0f89c4b287d6a3eb831',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.8.0',
            'version' => '7.8.0.0',
            'reference' => '1110f66a6530a40fe7aea0378fe608ee2b2248f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '111166291a0f8130081195ac4556a5587d7f1b5d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.6.1',
            'version' => '2.6.1.0',
            'reference' => 'be45764272e8873c72dbe3d2edcfdfcc3bc9f727',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'hamcrest/hamcrest-php' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8c3d0a3f6af734494ad8f6fbbee0ba92422859f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../hamcrest/hamcrest-php',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'illuminate/auth' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/broadcasting' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/bus' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/collections' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/config' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/console' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/container' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/contracts' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/cookie' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/database' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/encryption' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/events' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/hashing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/http' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/log' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/macroable' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/mail' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/notifications' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/pagination' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/pipeline' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/queue' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/redis' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/routing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/session' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/support' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/testing' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/translation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/validation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'illuminate/view' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v8.83.27',
            ),
        ),
        'intervention/gif' => array(
            'pretty_version' => '4.2.1',
            'version' => '4.2.1.0',
            'reference' => '6addac2c68b4bc0e37d0d3ccedda57eb84729c49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/gif',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'intervention/image' => array(
            'pretty_version' => '3.11.1',
            'version' => '3.11.1.0',
            'reference' => '0f87254688e480fbb521e2a1ac6c11c784ca41af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../intervention/image',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jaybizzle/crawler-detect' => array(
            'pretty_version' => 'v1.3.4',
            'version' => '1.3.4.0',
            'reference' => 'd3b7ff28994e1b0de764ab7412fa269a79634ff3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jaybizzle/crawler-detect',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jenssegers/agent' => array(
            'pretty_version' => 'v2.6.4',
            'version' => '2.6.4.0',
            'reference' => 'daa11c43729510b3700bc34d414664966b03bffe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jenssegers/agent',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kodova/hamcrest-php' => array(
            'dev_requirement' => true,
            'replaced' => array(
                0 => '*',
            ),
        ),
        'laravel/framework' => array(
            'pretty_version' => 'v8.83.27',
            'version' => '8.83.27.0',
            'reference' => 'e1afe088b4ca613fb96dc57e6d8dbcb8cc2c6b49',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/framework',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/helpers' => array(
            'pretty_version' => 'v1.6.0',
            'version' => '1.6.0.0',
            'reference' => '4dd0f9436d3911611622a6ced8329a1710576f60',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/helpers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/laravel' => array(
            'pretty_version' => 'dev-main',
            'version' => 'dev-main',
            'reference' => '095384197db7976f3a19e2e3869127c52866f4d9',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/sanctum' => array(
            'pretty_version' => 'v2.15.1',
            'version' => '2.15.1.0',
            'reference' => '31fbe6f85aee080c4dc2f9b03dc6dd5d0ee72473',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/sanctum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/serializable-closure' => array(
            'pretty_version' => 'v1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'e5a3057a5591e1cfe8183034b0203921abe2c902',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/serializable-closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/socialite' => array(
            'pretty_version' => 'v5.9.1',
            'version' => '5.9.1.0',
            'reference' => '49ecc4c907ed88c1254bae991c6b2948945645c2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/socialite',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/tinker' => array(
            'pretty_version' => 'v2.8.2',
            'version' => '2.8.2.0',
            'reference' => 'b936d415b252b499e8c3b1f795cd4fc20f57e1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/tinker',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravel/ui' => array(
            'pretty_version' => 'v3.4.6',
            'version' => '3.4.6.0',
            'reference' => '65ec5c03f7fee2c8ecae785795b829a15be48c2c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravel/ui',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'laravelcollective/html' => array(
            'pretty_version' => 'v6.4.1',
            'version' => '6.4.1.0',
            'reference' => '64ddfdcaeeb8d332bd98bef442bef81e39c3910b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../laravelcollective/html',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/commonmark' => array(
            'pretty_version' => '2.4.1',
            'version' => '2.4.1.0',
            'reference' => '3669d6d5f7a47a93c08ddff335e6d945481a1dd5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/commonmark',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/config' => array(
            'pretty_version' => 'v1.2.0',
            'version' => '1.2.0.0',
            'reference' => '754b3604fb2984c71f4af4a9cbe7b57f346ec1f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/flysystem' => array(
            'pretty_version' => '1.1.10',
            'version' => '1.1.10.0',
            'reference' => '3239285c825c152bcc315fe0e87d6b55f5972ed1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/flysystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/mime-type-detection' => array(
            'pretty_version' => '1.13.0',
            'version' => '1.13.0.0',
            'reference' => 'a6dfb1194a2946fcdc1f38219445234f65b35c96',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/mime-type-detection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/oauth1-client' => array(
            'pretty_version' => 'v1.10.1',
            'version' => '1.10.1.0',
            'reference' => 'd6365b901b5c287dd41f143033315e2f777e1167',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/oauth1-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mobiledetect/mobiledetectlib' => array(
            'pretty_version' => '2.8.45',
            'version' => '2.8.45.0',
            'reference' => '96aaebcf4f50d3d2692ab81d2c5132e425bca266',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mobiledetect/mobiledetectlib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mockery/mockery' => array(
            'pretty_version' => '1.6.6',
            'version' => '1.6.6.0',
            'reference' => 'b8e0bb7d8c604046539c1115994632c74dcb361e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mockery/mockery',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'monolog/monolog' => array(
            'pretty_version' => '2.9.1',
            'version' => '2.9.1.0',
            'reference' => 'f259e2b15fb95494c83f52d3caad003bbf5ffaa1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mtdowling/cron-expression' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '^1.0',
            ),
        ),
        'munafio/chatify' => array(
            'pretty_version' => 'v1.6.2',
            'version' => '1.6.2.0',
            'reference' => 'c7d3c63c56e60607ba1f90ccf93ebd38135cc1f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../munafio/chatify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.11.1',
            'version' => '1.11.1.0',
            'reference' => '7284c22080590fb39f2ffa3e9057f10a4ddd0e0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'nesbot/carbon' => array(
            'pretty_version' => '2.70.0',
            'version' => '2.70.0.0',
            'reference' => 'd3298b38ea8612e5f77d38d1a99438e42f70341d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nesbot/carbon',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/schema' => array(
            'pretty_version' => 'v1.2.4',
            'version' => '1.2.4.0',
            'reference' => 'c9ff517a53903b3d4e29ec547fb20feecb05b8ab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/schema',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nette/utils' => array(
            'pretty_version' => 'v4.0.2',
            'version' => '4.0.2.0',
            'reference' => 'cead6637226456b35e1175cc53797dd585d85545',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nette/utils',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v4.17.1',
            'version' => '4.17.1.0',
            'reference' => 'a6303e50c90c355c7eeee2c4a8b27fe8dc8fef1d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nunomaduro/collision' => array(
            'pretty_version' => 'v5.11.0',
            'version' => '5.11.0.0',
            'reference' => '8b610eef8582ccdc05d8f2ab23305e2d37049461',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nunomaduro/collision',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'opis/closure' => array(
            'pretty_version' => '3.6.3',
            'version' => '3.6.3.0',
            'reference' => '3d81e4309d2a927abbe66df935f4bb60082805ad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../opis/closure',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/sodium_compat' => array(
            'pretty_version' => 'v1.20.0',
            'version' => '1.20.0.0',
            'reference' => 'e592a3e06d1fa0d43988c7c7d9948ca836f644b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/sodium_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phar-io/manifest' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '97803eca37d319dfa7826cc2437fc020857acb53',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/manifest',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phar-io/version' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '4f7fd7836c6f332bb2933569e566a0d6c4cbed74',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phar-io/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phenx/php-font-lib' => array(
            'pretty_version' => '0.5.6',
            'version' => '0.5.6.0',
            'reference' => 'a1681e9793040740a405ac5b189275059e2a9863',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phenx/php-svg-lib' => array(
            'pretty_version' => '0.5.4',
            'version' => '0.5.4.0',
            'reference' => '46b25da81613a9cf43c83b2a8c2c1bdab27df691',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phenx/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'dd3a383e599f49777d8b628dadbb90cae435b87e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpunit/php-code-coverage' => array(
            'pretty_version' => '9.2.29',
            'version' => '9.2.29.0',
            'reference' => '6a3a87ac2bbe33b25042753df8195ba4aa534c76',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-code-coverage',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-file-iterator' => array(
            'pretty_version' => '3.0.6',
            'version' => '3.0.6.0',
            'reference' => 'cf1c2e7c203ac650e352f4cc675a7021e7d1b3cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-file-iterator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-invoker' => array(
            'pretty_version' => '3.1.1',
            'version' => '3.1.1.0',
            'reference' => '5a10147d0aaf65b58940a0b72f71c9ac0423cc67',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-invoker',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-text-template' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => '5da5f67fc95621df9ff4c4e5a84d6a8a2acf7c28',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-text-template',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/php-timer' => array(
            'pretty_version' => '5.0.3',
            'version' => '5.0.3.0',
            'reference' => '5a63ce20ed1b5bf577850e2c4e87f4aa902afbd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/php-timer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'phpunit/phpunit' => array(
            'pretty_version' => '9.6.13',
            'version' => '9.6.13.0',
            'reference' => 'f3d767f7f9e191eab4189abe41ab37797e30b1be',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpunit/phpunit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'psr/clock' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'e41a24703d4560fd0acb709162f73b8adfc3aa0d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/clock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/clock-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/container' => array(
            'pretty_version' => '1.1.2',
            'version' => '1.1.2.0',
            'reference' => '513e0666f7216c7459170d56df27dfcefe1689ea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => '0955afe48220520692d2d09f7ab7e0f93ffd6a31',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'e616d01114759c4c489f93b099585439f795fe35',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'ef29f6d262798707a9edd554e2b82517ef3a9376',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
                1 => '1.0|2.0',
            ),
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '408d5eafb83c57f6365a3ca330ff23aa4a5fa39b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.11.21',
            'version' => '0.11.21.0',
            'reference' => 'bcb22101107f3bf770523b65630c9d547f60c540',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pusher/pusher-php-server' => array(
            'pretty_version' => '7.2.3',
            'version' => '7.2.3.0',
            'reference' => '416e68dd5f640175ad5982131c42a7a666d1d8e9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pusher/pusher-php-server',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/collection' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'ad7475d1c9e70b190ecffc58f2d989416af339b4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ramsey/uuid' => array(
            'pretty_version' => '4.7.4',
            'version' => '4.7.4.0',
            'reference' => '60a4c63ab724854332900504274f6150ff26d286',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ramsey/uuid',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rhumsaa/uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '4.7.4',
            ),
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.7.0',
            'version' => '8.7.0.0',
            'reference' => 'f414ff953002a9b18e3a116f5e462c56f21237cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/cli-parser' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '442e7c7e687e42adc03470c7b668bc4b2402c0b2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/cli-parser',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit' => array(
            'pretty_version' => '1.0.8',
            'version' => '1.0.8.0',
            'reference' => '1fc9f64c0927627ef78ba436c9b17d967e68e120',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/code-unit-reverse-lookup' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => 'ac91f01ccec49fb77bdc6fd1e548bc70f7faa3e5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/code-unit-reverse-lookup',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/comparator' => array(
            'pretty_version' => '4.0.8',
            'version' => '4.0.8.0',
            'reference' => 'fa0f136dd2334583309d32b62544682ee972b51a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/comparator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/complexity' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => '739b35e53379900cc9ac327b2147867b8b6efd88',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/complexity',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => '74be17022044ebaaecfdf0c5cd504fc9cd5a7131',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/environment' => array(
            'pretty_version' => '5.1.5',
            'version' => '5.1.5.0',
            'reference' => '830c43a844f1f8d5b7a1f6d6076b784454d8b7ed',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/environment',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/exporter' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'ac230ed27f0f98f597c8a2b6eb7ac563af5e5b9d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/exporter',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/global-state' => array(
            'pretty_version' => '5.0.6',
            'version' => '5.0.6.0',
            'reference' => 'bde739e7565280bda77be70044ac1047bc007e34',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/global-state',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/lines-of-code' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'c1c2e997aa3146983ed888ad08b15470a2e22ecc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/lines-of-code',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-enumerator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => '5c9eeac41b290a3712d88851518825ad78f45c71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-enumerator',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/object-reflector' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'b4f479ebdbf63ac605d183ece17d8d7fe49c15c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/object-reflector',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/recursion-context' => array(
            'pretty_version' => '4.0.5',
            'version' => '4.0.5.0',
            'reference' => 'e75bd0f07204fec2a0af9b0f3cfe97d05f92efc1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/recursion-context',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/resource-operations' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '0f4443cb3a1d92ce809899753bc0d5d5a8dd19a8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/resource-operations',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/type' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '75e2c2a32f5e0b3aef905b9ed0b179b953b3d7c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/type',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'sebastian/version' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'c6c1022351a901512170118436c764e473f6de8c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/version',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'spatie/laravel-activitylog' => array(
            'pretty_version' => '3.17.0',
            'version' => '3.17.0.0',
            'reference' => 'bdc44862aaca39ecbd824133b80dbd7c8017ed7f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-activitylog',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'spatie/laravel-permission' => array(
            'pretty_version' => '5.11.0',
            'version' => '5.11.0.0',
            'reference' => '0a35e99da4cb6f85b07b3b58b718ff659c39a009',
            'type' => 'library',
            'install_path' => __DIR__ . '/../spatie/laravel-permission',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stichoza/google-translate-php' => array(
            'pretty_version' => 'v5.1.3',
            'version' => '*******',
            'reference' => '0d3de083a4b286868c4456cc76448a439a5aae07',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stichoza/google-translate-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'stripe/stripe-php' => array(
            'pretty_version' => 'v13.1.0',
            'version' => '********',
            'reference' => '8971caa149b4689e67c1123d3604e387c79ab543',
            'type' => 'library',
            'install_path' => __DIR__ . '/../stripe/stripe-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'swiftmailer/swiftmailer' => array(
            'pretty_version' => 'v6.3.0',
            'version' => '*******',
            'reference' => '8a5d5072dca8f48460fce2f4131fcc495eec654c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../swiftmailer/swiftmailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v5.4.28',
            'version' => '********',
            'reference' => 'f4f71842f24c2023b91237c72a365306f3c58827',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/css-selector' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '********',
            'reference' => 'f1d00bddb83a4cb2138564b2150001cb6ce272b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/css-selector',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '26954b3d62a6c5fd0ea8a2a00c0353a14978d05c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v5.4.26',
            'version' => '5.4.26.0',
            'reference' => 'b26719213a39c9ba57520cbc5e52bfcc5e8d92f9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '********',
            'reference' => '2eaf8e63bc5b8cefabd4a800157f0d0c094f677a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => '7bc61cc2db649b4637d331240c5346dcc7708051',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v5.4.27',
            'version' => '5.4.27.0',
            'reference' => 'ff4bce3c33451e7ec778070e45bd23f74214cd5d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v5.4.28',
            'version' => '********',
            'reference' => '365992c83a836dfe635f1e903ccca43ee03d3dd2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v5.4.28',
            'version' => '********',
            'reference' => '127a2322ca1828157901092518b8ea8e4e1109d4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v5.4.26',
            'version' => '5.4.26.0',
            'reference' => '2ea06dfeee20000a319d8407cea1d47533d5a9d2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'ea208ce43cbb04af6867b4fdddb1bdbf84cc28cb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '6de50471469b8c9afc38164452ab2b6170ee71c1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '875e90aeea2777b6f135677f618529449334a612',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'ecaafce9f77234a6a449d29e49267ba10499116d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '8c4ad05dd0120b6a53c1ca374dca2ad0a1c4ed92',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '42292d99c55abe617799667f454222c54c60e229',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '70f4aebd92afca2f865444d30a4d2151c13c3179',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php73' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => 'fe2f306d1d9d346a7fee353d0d5012e401e984b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php73',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '6caa57379c4aec19c0a12a38b59b26487dcfe4b5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.28.0',
            'version' => '1.28.0.0',
            'reference' => '7581cd600fa9fd681b797d00b02f068e2f13263b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v5.4.28',
            'version' => '********',
            'reference' => '45261e1fccad1b5447a8d7a8e67aa7b4a9798b7b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v5.4.26',
            'version' => '5.4.26.0',
            'reference' => '853fc7df96befc468692de0a48831b38f04d2cb2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'reference' => '4b426aac47d6427cc1a1d0f7e2ac724627f5966c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '********',
            'reference' => 'd9e72497367c23e08bf94176d2be45b00a9d232a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation' => array(
            'pretty_version' => 'v6.0.19',
            'version' => '********',
            'reference' => '9c24b3fdbbe9fb2ef3a6afd8bbaadfd72dad681f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'acbfbb274e730e5a0236f619b6168d9dedb3e282',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.3|3.0',
            ),
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v5.4.28',
            'version' => '********',
            'reference' => '684b36ff415e1381d4a943c3ca2502cd2debad73',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theseer/tokenizer' => array(
            'pretty_version' => '1.2.1',
            'version' => '1.2.1.0',
            'reference' => '34a41e998c2183e22995f158c581e7b5e755ab9e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theseer/tokenizer',
            'aliases' => array(),
            'dev_requirement' => true,
        ),
        'tijsverkoyen/css-to-inline-styles' => array(
            'pretty_version' => '2.2.6',
            'version' => '2.2.6.0',
            'reference' => 'c42125b83a4fa63b187fdf29f9c93cb7733da30c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tijsverkoyen/css-to-inline-styles',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.5.0',
            'version' => '5.5.0.0',
            'reference' => '1a7ea2afc49c3ee6d87061f5a233e3a035d0eae7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '1.6.1',
            'version' => '1.6.1.0',
            'reference' => '87337c91b9dfacee02452244ee14ab3c43bc485a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webmozart/assert' => array(
            'pretty_version' => '1.11.0',
            'version' => '1.11.0.0',
            'reference' => '11cb2199493b2f8a3b53e7f19068fc6aac760991',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webmozart/assert',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'yajra/laravel-datatables-oracle' => array(
            'pretty_version' => 'v9.21.2',
            'version' => '9.21.2.0',
            'reference' => 'a7fd01f06282923e9c63fa27fe6b391e21dc321a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../yajra/laravel-datatables-oracle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
