<?php

namespace App\Http\Controllers\Api;

use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\VerificationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * Create User
     * @param Request $request
     * @return User 
     */
    public function createUser(Request $request, AuthService $authService)
    {
        try {
            //Validated
            $validateUser = Validator::make(
                $request->all(),
                [
                    // 'name' => 'required',
                    'first_name' => 'required|alpha',
                    'last_name' => 'required|alpha',
                    'email' => "required|email|unique:users,email|regex:/(.+)@(.+)\.(.+)/i",
                    'password' => [
                        'required',
                        'confirmed',
                        'min:8',
                        'max:32',
                        'regex:/[A-Z]/',      // at least one uppercase letter
                        'regex:/[a-z]/',      // at least one lowercase letter
                        'regex:/[0-9]/',      // at least one digit
                        'regex:/[@#$%^&*()_+\-=\[\]{};:"\\|,.<>\/?]+/', // at least one special character
                    ],
                    'phone' => 'required|numeric',
                    'device_token' => 'required',
                    'device_type' => 'required',
                ],
                [
                    "password.min" => 'Password must be at least 8 characters.',
                    "password.max" => 'Password may not be greater than 32 characters.',
                    "password.regex" => 'Password must contain at least one uppercase letter, one lowercase letter, one digit, and one special character.',
                    "password.confirmed" => 'Passwords do not match.'
                ]
            );

            if ($validateUser->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validateUser->errors()->first(),
                    'data' => null
                ], 401);
            }
            return $authService->signup($request->all());
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * Login The User
     * @param Request $request
     * @return User
     */
    public function loginUser(Request $request)
    {
        try {
            $validateUser = Validator::make($request->all(),[
                    'email' => 'required|email',
                    'password' => 'required|min:8',
                    "device_token" => 'required',
                    "device_type" => 'required'
                ],[
                    "device_token.required" => "device token is required",
                    "device_type.required" => "device type is required",
                ]
            );
            if ($validateUser->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validateUser->errors()->first(),
                    'data' => null
                ], 401);
            }

            if (!Auth::attempt($request->only(['email', 'password']))) {
                // activity("User")->causedBy($request->email)->log("LoggedIn fail");
                return response()->json([
                    'status' => false,
                    'message' => 'Email & Password does not match with our record.',
                    'data' => null
                ], 401);
            }
            $user = User::where('email', $request->email)->first();
            if($user->email_verified == 0){
                return api_response(false, 'Email is not verified');
            }
            $user->device_token = $request->device_token;
            $user->device_type = $request->device_type;
            $user->save();

            // Track user session for API login
            $sessionTrackingService = app(\App\Services\SessionTrackingService::class);
            $sessionTrackingService->createSession($request, $user);

            activity($user->name)->performedOn($user)->causedBy($user)->log("LoggedIn");
            return response()->json([
                'status' => true,
                'message' => 'User Logged In Successfully',
                'data' => [
                    "user" => $user,
                    'token' => $user->createToken("API TOKEN")->plainTextToken
                ],
            ], 200);
        } catch (\Throwable $th) {
            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }
    function resend_otp(Request $request, VerificationService $verfication){
        $validator = Validator::make($request->all(), [
            "email" => "required|email", 
            "type" => "required|in:email,password"
        ],
        [
            "type.in" => "The selected type is invalid. Allowed values are email or password."
        ]);
        if($validator->fails()){
            return ["status" => false, "message" => $validator->errors()->first(), "data" => null];
        }else{
            if($request->type == "email"){
                return $verfication->send_otp($request->email, "email", "email-templates.email-verify", "Email verification");
            }elseif($request->type == "password"){
                return $verfication->send_otp($request->email, "password", "email-templates.forget-password", "Forget Password");
            }
        }
    }

    function check_otp(Request $request, VerificationService $verfication_service){
        return $verfication_service->verify_otp($request->all());
    }
    function email_verification(Request $request, VerificationService $verfication_service = null){
        return $verfication_service->email_verify($request->all());
    }

    function forget_password(Request $request, VerificationService $verfication_service){
        return $verfication_service->forget_password($request->all());
    }

    function reset_password(Request $request, AuthService $authService){
        return $authService->reset_password($request->all());
    }
}
