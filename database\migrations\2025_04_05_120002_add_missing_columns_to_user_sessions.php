<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddMissingColumnsToUserSessions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_sessions', function (Blueprint $table) {
            // Add missing columns that were in the original table
            if (!Schema::hasColumn('user_sessions', 'device_info')) {
                $table->string('device_info')->nullable()->after('device_type');
            }
            if (!Schema::hasColumn('user_sessions', 'browser')) {
                $table->string('browser')->nullable()->after('device_info');
            }
            if (!Schema::hasColumn('user_sessions', 'platform')) {
                $table->string('platform')->nullable()->after('browser');
            }
            if (!Schema::hasColumn('user_sessions', 'location')) {
                $table->string('location')->nullable()->after('platform');
            }
            if (!Schema::hasColumn('user_sessions', 'country')) {
                $table->string('country')->nullable()->after('location');
            }
            if (!Schema::hasColumn('user_sessions', 'city')) {
                $table->string('city')->nullable()->after('region');
            }
            if (!Schema::hasColumn('user_sessions', 'login_time')) {
                $table->timestamp('login_time')->nullable()->after('last_activity');
            }
            if (!Schema::hasColumn('user_sessions', 'logout_time')) {
                $table->timestamp('logout_time')->nullable()->after('login_time');
            }
            if (!Schema::hasColumn('user_sessions', 'session_id')) {
                $table->string('session_id')->nullable()->after('logout_time');
            }
            if (!Schema::hasColumn('user_sessions', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('session_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_sessions', function (Blueprint $table) {
            $table->dropColumn([
                'device_info',
                'browser', 
                'platform',
                'location',
                'country',
                'city',
                'login_time',
                'logout_time',
                'session_id',
                'is_active'
            ]);
        });
    }
}
