<?php

namespace App\Models;

use App\Booking;
use App\Card;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use App\HasRoles;
use App\Listing;
use App\Profile;
use App\Review;
use App\Traits\HasUuid;
use Carbon\Carbon;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\Traits\LogsActivity;

class User extends Authenticatable
{
    use Notifiable, HasRoles, SoftDeletes, HasApiTokens, LogsActivity, HasUuid;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'phone',
        'email',
        'password',
        "device_type",
        "device_token",
        "avatar"
    ];
    protected $appends = ["last_active", "total_spent", "total_revenue",  "rating"];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        "provider_bookings"
    ];


    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function listings()
    {
        return $this->hasMany(Listing::class, "user_id");
    }

    public function permissionsList()
    {
        $roles = $this->roles;
        $permissions = [];
        foreach ($roles as $role) {
            $permissions[] = $role->permissions()->pluck('name')->implode(',');
        }
        return collect($permissions);
    }

    public function permissions()
    {
        $permissions = [];
        $role = $this->roles->first();
        $permissions = $role->permissions()->get();
        return $permissions;
    }

    public function isAdmin()
    {
        $is_admin = $this->roles()->where('name', 'admin')->first();
        if ($is_admin != null) {
            $is_admin = true;
        } else {
            $is_admin = false;
        }
        return $is_admin;
    }

    public function getLastActiveAttribute()
    {
        return Carbon::parse($this->last_active_at)->diffForHumans();
    }
    function customer_bookings()
    {
        return $this->hasMany(Booking::class, "user_id");
    }
    public function getTotalSpentAttribute()
    {
        return $this->customer_bookings()
            ->where(function ($query) {
                $query->where('status', 0)
                    ->orWhere('status', 1);
            })
            ->sum('total_amount');
    }
    function provider_review()
    {
        return $this->hasMany(Review::class, "provider_id");
    }
    public function provider_bookings()
    {
        return $this->hasMany(Booking::class, "provider_id");
    }
    public function getTotalRevenueAttribute()
    {
        // return $this->provider_bookings->sum('total_amount');
        return $this->provider_bookings()
            ->where(function ($query) {
                $query->where('status', 0)
                    ->orWhere('status', 1);
            })
            ->sum('total_amount');
    }
    function provider_rating()
    {
        return $this->hasMany(Review::class, "provider_id");
    }
    function customer_rating()
    {
        return $this->hasMany(Review::class, "user_id");
    }
    function active_bookings_provider()
    {
        return $this->hasMany(Booking::class, "provider_id")->where("status", 0);
    }
    function active_bookings_customer()
    {
        return $this->hasMany(Booking::class, "user_id")->where("status", 0);
    }
    function card()
    {
        return $this->hasOne(Card::class)->where("is_default", 1);
    }
    function wishlists(){
        return $this->hasMany(Wishlist::class);
    }
    function new_messages(){
        return $this->hasMany(ChMessage::class, "to_id")->where("seen", 0);
    }

    /**
     * Get all user sessions.
     */
    public function userSessions()
    {
        return $this->hasMany(UserSession::class);
    }

    /**
     * Get active user sessions.
     */
    public function activeUserSessions()
    {
        return $this->hasMany(UserSession::class)->where('is_active', true);
    }
    function getratingAttribute($value = null)
    { 
        $reviews = Review::where("provider_id", $this->id)->get();
        $rating = 0;
        if (count($reviews) > 5) {
            $rating = $reviews->avg("rating");
            $rating = round($rating);
            $rating = max(1, min(5, $rating));
            $rating = number_format($rating, 1);
        }
        return $rating;
    }
}
