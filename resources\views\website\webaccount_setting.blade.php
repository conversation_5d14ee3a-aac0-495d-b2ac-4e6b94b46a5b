@extends('website.layout.master')


@push('css')
    <style>
        [tooltip] {
            position: relative;
        }

        [tooltip]::before,
        [tooltip]::after {
            text-transform: none;
            font-size: .9em;
            line-height: 1;
            user-select: none;
            pointer-events: none;
            position: absolute;
            display: none;
            opacity: 0;
        }

        [tooltip]::before {
            content: '';
            border: 5px solid transparent;
            z-index: 1001;
        }

        [tooltip]::after {
            content: attr(tooltip);
            font-family: Helvetica, sans-serif;
            text-align: center;
            min-width: 15em;
            max-width: 21em;
            /* white-space: nowrap; */
            overflow: hidden;
            /* text-overflow: ellipsis; */
            padding: 1ch 1.5ch;
            border-radius: .3ch;
            box-shadow: 0 1em 2em -.5em rgba(0, 0, 0, 0.35);
            background: #333;
            color: #fff;
            z-index: 1000;
        }

        [tooltip]:hover::before,
        [tooltip]:hover::after {
            display: block;
        }

        [tooltip='']::before,
        [tooltip='']::after {
            display: none !important;
        }

        [tooltip]:not([flow])::before,
        [tooltip][flow^="up"]::before {
            bottom: 100%;
            border-bottom-width: 0;
            border-top-color: #333;
        }

        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::after {
            bottom: calc(100% + 5px);
        }

        [tooltip]:not([flow])::before,
        [tooltip]:not([flow])::after,
        [tooltip][flow^="up"]::before,
        [tooltip][flow^="up"]::after {
            left: 50%;
            transform: translate(-50%, -.5em);
        }

        [tooltip][flow^="down"]::before {
            top: 100%;
            border-top-width: 0;
            border-bottom-color: #333;
        }

        [tooltip][flow^="down"]::after {
            top: calc(100% + 5px);
        }

        [tooltip][flow^="down"]::before,
        [tooltip][flow^="down"]::after {
            left: 50%;
            transform: translate(-50%, .5em);
        }

        [tooltip][flow^="left"]::before {
            top: 50%;
            border-right-width: 0;
            border-left-color: #333;
            left: calc(0em - 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="left"]::after {
            top: 50%;
            right: calc(100% + 5px);
            transform: translate(-.5em, -50%);
        }

        [tooltip][flow^="right"]::before {
            top: 50%;
            border-left-width: 0;
            border-right-color: #333;
            right: calc(0em - 5px);
            transform: translate(.5em, -50%);
        }

        [tooltip][flow^="right"]::after {
            top: 50%;
            left: calc(100% + 5px);
            transform: translate(.5em, -50%);
        }

        @keyframes tooltips-vert {
            to {
                opacity: .9;
                transform: translate(-50%, 0);
            }
        }

        @keyframes tooltips-horz {
            to {
                opacity: .9;
                transform: translate(0, -50%);
            }
        }

        [tooltip]:not([flow]):hover::before,
        [tooltip]:not([flow]):hover::after,
        [tooltip][flow^="up"]:hover::before,
        [tooltip][flow^="up"]:hover::after,
        [tooltip][flow^="down"]:hover::before,
        [tooltip][flow^="down"]:hover::after {
            animation: tooltips-vert 300ms ease-out forwards;
        }

        [tooltip][flow^="left"]:hover::before,
        [tooltip][flow^="left"]:hover::after,
        [tooltip][flow^="right"]:hover::before,
        [tooltip][flow^="right"]:hover::after {
            animation: tooltips-horz 300ms ease-out forwards;
        }
    </style>
@endpush


@section('content')
    <section class="det_form setting_page">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h4>Account Settings</h4>
                    <div class="row">
                        {{-- <form action="{{ route('userprofile.profile_img') }}" method="POST" enctype="multipart/form-data"> --}}
                        <form action="" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="col-md-12">
                                <div class="profile_img_tooltip_wrapper d-flex gap-3 align-items-center mt-4">
                                    <div class="profile_picture">
                                        <div class="profile-image-div m-0">
                                            <img id="blah" src="{{ asset('website') . '/' . auth()->user()->avatar }}"
                                                alt="your image" />
                                            <input type='file' name="profile_img" id="profile_img" />
                                            <label for="profile_img" class="profile_img_edit pencil_icon"><i
                                                    class="fas fa-pencil-alt"></i></label>
                                        </div>
                                        @error('profile_img')
                                            <p class="text-danger mb-0">{{ $message }}</p>
                                        @enderror
                                        {{-- <div class="form_field_padding setting_btn ms-3">
                                            <input class="btn setting_btn" style="padding-block: 10px;padding-inline: 63px;"
                                                type="submit" value="Update">
                                        </div> --}}
                                    </div>
                                    <div class="profile_tooltip">
                                        <label
                                            tooltip="Upload icon in PNG, JPEG or SVG and recommended dimension is 30 x 30"
                                            for=""><i class="fas fa-info-circle"></i></label>
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                    <div class="row">
                        <div class="col-md-12">

                        </div>
                    </div>
                    <div class="wh-box mt-4">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="box_head">
                                    <h4>Personal Information</h4>
                                    <div class="d-flex gap-2 align-items-center">
                                        <div class="btn-row ">
                                            <button type="button" class="btn m-0" href="#" data-bs-toggle="modal"
                                                data-bs-target="#change">Change Password</button>
                                        </div>
                                        <a href="javascript:void(0)" class="edit-sec pencil_icon">
                                            <i class="fas fa-pencil-alt"></i>
                                            {{-- <img  src="{{ asset('website') }}/images/edit.png" alt="your image" /> --}}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <form id="profileForm" action="{{ route('userprofile.update') }}" method="POST">
                                    @csrf
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input type="text" name="first_name"
                                                    value="{{ auth()->user()->first_name ?? '' }}"
                                                    class="form-control {{ auth()->user()->hasRole('user') ? '' : 'disabled_toggle' }}  @error('first_name') is-invalid @enderror"
                                                    placeholder="First Name" disabled>
                                                @error('first_name')
                                                    <p class="text-danger mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input type="text" name="last_name"
                                                    value="{{ auth()->user()->last_name ?? '' }}"
                                                    class="form-control {{ auth()->user()->hasRole('user') ? '' : 'disabled_toggle' }}  @error('last_name') is-invalid @enderror"
                                                    placeholder="Last Name(s)" disabled>
                                                @error('last_name')
                                                    <p class="text-danger mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input type="hidden" name="phone" class="country_code_merged"
                                                    value="">
                                                <input type="tel" name=""
                                                    value="{{ auth()->user()->phone ?? '' }}" id="telephone_profile"
                                                    class="form-control country_code_profile disabled @error('phone') is-invalid @enderror"
                                                    placeholder="Phone" pattern="^\d+$">
                                                @error('phone')
                                                    <p class="text-danger mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                        </div>
                                        {{-- <div class="col-md-6 d-flex gap-2 align-items-center box_head"> --}} {{-- Before removing edit email button (FOR BACKUP) --}}
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input type="email" name="email"
                                                    value="{{ auth()->user()->email ?? '' }}"
                                                    class="form-control email_default @error('email') is-invalid @enderror"
                                                    placeholder="Email" disabled
                                                    data-email="{{ auth()->user()->email ?? '' }}">
                                                @error('email')
                                                    <p class="text-danger mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                            {{-- <a href="" data-bs-toggle="modal" data-bs-target="#change_email"
                                                class="edit">
                                                <img src="{{ asset('website') }}/images/edit.png" alt="your image" />
                                            </a> --}}
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input type="text" name="town"
                                                    class="form-control disabled @error('town') is-invalid @enderror"
                                                    value="{{ auth()->user()->profile->city ?? '' }}"
                                                    placeholder="Town/City">
                                                @error('town')
                                                    <p class="text-danger mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input type="number" name="postal"
                                                    class="form-control disabled @error('postal') is-invalid @enderror"
                                                    placeholder="Zipcode/Postal"
                                                    value="{{ auth()->user()->profile->postal ?? '' }}">
                                                @error('postal')
                                                    <p class="text-danger mt-1">{{ $message }}</p>
                                                @enderror
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form_field_padding">
                                                <input class="btn setting_btn" id="profile_update" type="submit"
                                                    value="Save">
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="wh-box mt-4">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="box_head">
                                    <h4>Session History</h4>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Session ID</th>
                                                <th>Start Time</th>
                                                <th>End Time</th>
                                                <th>Duration</th>
                                                <th>IP Address</th>
                                                <th>Location</th>
                                                <th>Device</th>
                                                <th>Browser</th>
                                                <th>Platform</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($user_sessions as $session)
                                                <tr>
                                                    <td>{{ $session->session_id }}</td>
                                                    <td>{{ $session->login_time->format('Y-m-d H:i:s') }}</td>
                                                    <td>{{ $session->logout_time ? $session->logout_time->format('Y-m-d H:i:s') : '-' }}</td>
                                                    <td>{{ $session->logout_time ? $session->logout_time->diffForHumans($session->login_time) : '-' }}</td>
                                                    <td>{{ $session->ip_address }}</td>
                                                    <td>{{ $session->location }}</td>
                                                    <td>{{ $session->device_type }}</td>
                                                    <td>{{ $session->browser }}</td>
                                                    <td>{{ $session->platform }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>



                    </div>

                    <div class="wh-box">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="box_head">
                                    <div>
                                        <h4>Payment Methods</h4>
                                        <p>Add and mange your payment methods</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="row pt-1">
                                    <div class="col-md-12">
                                        {{-- <div class="box_head">
                                            <h4>Cards</h4>
                                        </div> --}}
                                        <div class="card_wrapper">
                                            @foreach ($cards['records'] ?? [] as $card)
                                                <div
                                                    class="card_parent d-flex justify-content-between align-items-center divider py-3">
                                                    <div class="d-flex justify-content-between w-90 align-items-center">
                                                        <div class="d-flex gap-3 align-items-center">
                                                            @php
                                                                $cardNumber = $card['fields']['card_number'] ?? '';
                                                                $maskedCardNumber = str_replace('X', '•', $cardNumber);
                                                                $cardType = $card['fields']['card_type'] ?? '';
                                                                $cardTypeImg = strtolower(
                                                                    $card['fields']['card_type'] ?? '',
                                                                );
                                                            @endphp
                                                            <div class="card_type">
                                                                <div class="cards-img">
                                                                    @if ($cardTypeImg == 'visa')
                                                                        <img src="{{ asset('website/images/visa_new.png') }}"
                                                                            alt="Visa" height="40px" width="40px"
                                                                            class="img-fluid">
                                                                    @elseif ($cardTypeImg == 'mastercard')
                                                                        <img src="{{ asset('website/images/master_card_new.png') }}"
                                                                            alt="Mastercard" height="40px"
                                                                            width="40px" class="img-fluid">
                                                                    @elseif ($cardTypeImg == 'discover')
                                                                        <img src="{{ asset('website/images/discover_new.png') }}"
                                                                            alt="Discover" height="40px"
                                                                            width="40px" class="img-fluid">
                                                                    @elseif ($cardTypeImg == 'american express')
                                                                        <img src="{{ asset('website/images/american_express_new.png') }}"
                                                                            alt="American" height="40px"
                                                                            width="40px" class="img-fluid">
                                                                    @elseif ($cardTypeImg == 'jcb')
                                                                        <img src="{{ asset('website/images/JCB_new.png') }}"
                                                                            alt="JCB" height="40px"
                                                                            width="40px" class="img-fluid">
                                                                    @elseif ($cardTypeImg == 'diners club')
                                                                        <img src="{{ asset('website/images/diners_club_new.png') }}"
                                                                            alt="Diners Club" height="40px"
                                                                            width="40px" class="img-fluid">
                                                                    @elseif ($cardTypeImg == 'unionpay')
                                                                        <img src="{{ asset('website/images/union_pay_new.png') }}"
                                                                            alt="Union Pay" height="40px"
                                                                            width="40px" class="img-fluid">
                                                                    @endif
                                                                </div>
                                                            </div>
                                                            <div class="card_info">
                                                                <div class="card_num">
                                                                    {{ $cardType }}
                                                                    {{ $maskedCardNumber }}
                                                                </div>
                                                                <div class="card_exp">
                                                                    Expiration: {{ $card['fields']['expiry_date'] ?? '' }}
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @if (($card['fields']['is_default'] ?? '') == 1)
                                                            <div class="default_badge badge">
                                                                Default
                                                            </div>
                                                        @endif
                                                    </div>


                                                    <div class="dropdown">
                                                        <button class="btn btn_trans" type="button"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <div class="dropdown-menu">
                                                            <div class="dropdown-item">
                                                                @if (($card['fields']['is_default'] ?? '') != 1)
                                                                    <button class="btn btn_trans deafult_btn p-0"
                                                                        onclick="location.href = `{{ route('cards.default', $card['fields']['skyflow_id']) }}`">Set
                                                                        as
                                                                        default</button>
                                                                @endif
                                                            </div>
                                                            <div class="dropdown-item">
                                                                <button class="btn btn_trans remove_btn p-0"
                                                                    onclick="location.href = `{{ route('cards.delete', $card['fields']['skyflow_id']) }}`">Remove</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>

                                <button class="btn setting_btn button button1 mt-3" data-bs-toggle="modal"
                                    data-bs-target="#add_card">Add Payment Method</button>

                                {{-- Remove this when dynamic done --}}
                                <table class="table cards_table d-none">
                                    <thead>
                                        <tr>
                                            {{-- <th>ID</th> --}}
                                            <th>Card Number</th>
                                            {{-- <th>Card Holder Name</th>
                                            <th>Expiry Date</th> --}}
                                            {{-- <th>CVC</th> --}}
                                            {{-- <th></th> --}}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($cards["records"] ?? [] as $card)
                                            <tr>
                                                <td>{{ $card['fields']['card_number'] ?? '' }}</td>
                                                <td>
                                                </td>
                                                {{-- <td>{{ $card['fields']['cardholder_name'] ?? '' }}</td>
                                                <td>{{ $card['fields']['expiry_date'] ?? '' }}</td> --}}

                                                {{-- <td>
                                                    <div class="row">
                                                        <input type="number" class="col-md-3 form-control">
                                                    </div>
                                                    {{ decrypt($card->cvc) }}
                                                </td> --}}
                                                {{-- <td>
                                                    @if (!$card->is_default)
                                                        <button class="btn btn-secondary"
                                                            onclick="location.href = `{{ route('default_card', $card->id) }}`"><i
                                                                class="fas fa-check"></i></button>
                                                    @endif
                                                    <button class="btn btn-danger"
                                                        onclick="location.href = `{{ route('delete_card', $card->id) }}`"><i
                                                            class="fas fa-trash"></i></button>
                                                </td> --}}
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="8">
                                                    <h6 class="text-center">You don’t have any saved cards</h6>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec-2-profileSetting" id="verfication-kyc">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="img d-flex justify-content-between align-items-center">
                        <h3 class="text-uppercase semi-bold text-shadow"><span
                                class="white d-block fs-66 pb-3 bold ">LUXUSTARS</span>
                            identity verification
                        </h3>
                        @if (auth()->user()->identity_verified == 'verified')
                            <img src="{{ asset('website/images/check.png') }}" height="135" alt="">
                        @else
                            <a href="{{ route('stripe_custom_page') }}" class="button">Verify now > </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </section>

    @include('website.layout.resetEmail')
    @include('website.layout.add_card')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cleave.js/1.6.0/cleave.min.js"></script>
    <script>
        // function readURL(input) {
        //     if (input.files && input.files[0]) {
        //         var reader = new FileReader();
        //         reader.onload = function(e) {
        //             $('#blah')
        //                 .attr('src', e.target.result);
        //         };
        //         reader.readAsDataURL(input.files[0]);
        //     }
        // }


        var $telephone = $("#telephone_profile");
        $telephone.intlTelInput({
            separateDialCode: true,
        });

        $telephone.on("countrychange", function() {
            // var country_code = $(".iti__selected-dial-code").html();
            // $(".country_code_profile").val(country_code);
            mergeDialCode();
        });

        $telephone.on("keyup", function() {
            mergeDialCode();
        });

        $(document).ready(function() {
            mergeDialCode();
        })

        function mergeDialCode() {
            var dialCode = $(".iti__selected-dial-code").html();
            var number = $("#telephone_profile").val();
            var merged = dialCode + number;
            $(".country_code_merged").val(merged);
        }



        $('#profile_img').on('change', function() {
            let fileInput = this;

            if (fileInput.files && fileInput.files[0]) {
                let formData = new FormData();
                formData.append('profile_img', fileInput.files[0]);

                $.ajax({
                    url: '{{ route('userprofile.profile_img') }}',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: response.message
                        });
                        console.log(response.image_url);
                        if (response.image_url) {
                            $('#blah').attr('src', response.image_url);
                        }
                    },
                    error: function(xhr) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: xhr.responseJSON.message
                        });
                    }
                });
            }
        });


        // $(document).ready(function() {
        //     $('[name="card_holder_name"]').on('keydown keyup input', function(event) {
        //         var value = $(this).val();
        //         var filteredValue = value.replace(/[0-9]/g, '');
        //         if (value !== filteredValue) {
        //             $(this).val(filteredValue);
        //         }
        //     });
        // });
    </script>
    <script>
        $(document).ready(function() {
            $('.wh-box input').prop('disabled', true);
            $('.edit-sec').click(function() {
                var parent = $(this).closest('.wh-box');
                // var inputs = $(parent).find('input:not(.email_default):not(.disabled_toggle)');
                var inputs = $(parent).find('input:not(.disabled_toggle)');

                $(this).toggleClass('active');
                inputs.each(function() {
                    var input = $(this);
                    input.prop('disabled', !input.prop('disabled'));
                });
            });

            // var cleave = new Cleave('.cardnumber', {
            //     creditCard: true,
            //     onCreditCardTypeChanged: function(type) {
            //         console.log(type);
            //     }
            // })
        });

        function moveToNext(current, nextFieldID) {
            if (current.value.length >= 2) {
                var nextField = document.getElementById(nextFieldID);
                if (nextField) {
                    nextField.focus();
                }
            }
        }
    </script>
    {{-- <script type="text/javascript" src="https://js.stripe.com/v2/"></script>
    <script type="text/javascript">
        $(function() {

            /*------------------------------------------
            --------------------------------------------
            Stripe Payment Code
            --------------------------------------------
            --------------------------------------------*/

            var $form = $(".require-validation");

            $('form.require-validation').bind('submit', function(e) {
                var $form = $(".require-validation"),
                    inputSelector = ['input[type=email]', 'input[type=password]',
                        'input[type=text]', 'input[type=file]',
                        'textarea'
                    ].join(', '),
                    $inputs = $form.find('.required').find(inputSelector),
                    $errorMessage = $form.find('div.error'),
                    valid = true;
                $errorMessage.addClass('hide');

                $('.has-error').removeClass('has-error');
                $inputs.each(function(i, el) {
                    var $input = $(el);
                    if ($input.val() === '') {
                        $input.parent().addClass('has-error');
                        $errorMessage.removeClass('hide');
                        e.preventDefault();
                    }
                });

                if (!$form.data('cc-on-file')) {
                    e.preventDefault();
                    Stripe.setPublishableKey($form.data('stripe-publishable-key'));
                    Stripe.createToken({
                        number: $('.card-number').val(),
                        cvc: $('.card-cvc').val(),
                        exp_month: $('.card-expiry-month').val(),
                        exp_year: $('.card-expiry-year').val()
                    }, stripeResponseHandler);
                }

            });

            /*------------------------------------------
            --------------------------------------------
            Stripe Response Handler
            --------------------------------------------
            --------------------------------------------*/
            function stripeResponseHandler(status, response) {
                if (response.error) {
                    let message = response.error.message;
                    if(response.error.message == "A network error has occurred, and you have not been charged. Please try again."){
                        message = "Please enter the card details and try again"
                    }
                    Swal.fire({
                        title: "Error",
                        text: message,
                        icon: "error"
                    });
                    $('.error')
                        .removeClass('hide')
                        .find('.alert')
                        .text(message);
                } else {
                    /* token contains id, last4, and card type */
                    var token = response['id'];

                    $form.find('input[type=text]').empty();
                    $form.append("<input type='hidden' name='stripeToken' value='" + token + "'/>");
                    $form.get(0).submit();
                }
            }

        });
    </script> --}}
    <script src="https://js.skyflow.com/v1/index.js"></script>

    <script>
        $(document).ready(function() {
            $(document).on("click", "#collectPCIData", function() {
                var $button = $(this);
                $button.prop("disabled", true);
                $('.spinner-border').removeClass('d-none'); // Show the spinner
                setTimeout(function() {
                    $button.prop("disabled", false);
                    $('.spinner-border').addClass('d-none');
                }, 3000);
            });
        });

        try {
            const skyflow = Skyflow.init({
                vaultID: "{{ skyflow_vault_id() }}",
                vaultURL: "{{ skyflow_vault_url() }}",
                getBearerToken: () => {
                    return new Promise((resolve, reject) => {
                        $.ajax({
                            url: "{{ route('getBearerToken') }}",
                            method: "GET",
                            data: {
                                type: "front_end"
                            },
                            success: function(response) {
                                resolve(response.accessToken);
                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                reject(errorThrown);
                            }
                        });
                    });
                },
                options: {
                    logLevel: Skyflow.LogLevel.ERROR,
                    // actual value of element can only be accessed inside the handler
                    // when the env is set to DEV.
                    // make sure the env is set to PROD when using skyflow-js in production
                    env: Skyflow.Env.DEV,
                }
            });

            // create collect Container
            const collectContainer = skyflow.container(Skyflow.ContainerType.COLLECT);

            //custom styles for collect elements
            const collectStylesOptions = {
                inputStyles: {
                    base: {
                        border: "1px solid #eae8ee",
                        padding: "10px 16px",
                        color: "#1d1d1d",
                        marginTop: "4px",
                        fontFamily: "Poppins, sans-serif",
                        paddingInline: "18px",
                        height: "50px",
                        borderRadius: "15px",
                        marginBottom: "5px",
                    },
                    complete: {
                        color: "#4caf50",
                    },
                    empty: {},
                    focus: {},
                    invalid: {
                        color: "#f44336",
                    },
                },
                labelStyles: {
                    base: {
                        fontFamily: "Poppins, sans-serif",
                        fontSize: "12px",
                        fontWeight: "bold",
                    },
                },
                errorTextStyles: {
                    base: {
                        color: "#f44336",
                    },
                },
            };

            // create collect elements
            const cardNumberElement = collectContainer.create({
                table: "credit_cards",
                column: "card_number",
                ...collectStylesOptions,
                placeholder: "Card Number",
                label: "Card Number",
                type: Skyflow.ElementType.CARD_NUMBER,
            }, {
                required: true
            });

            // const cvvElement = collectContainer.create({
            //     table: "credit_cards",
            //     column: "cvv",
            //     ...collectStylesOptions,
            //     label: "Cvv",
            //     placeholder: "cvv",
            //     type: Skyflow.ElementType.CVV,
            // });

            const expiryDateElement = collectContainer.create({
                table: "credit_cards",
                column: "expiry_date",
                ...collectStylesOptions,
                label: "Expiry Date",
                placeholder: "MM/YYYY",
                type: Skyflow.ElementType.EXPIRATION_DATE,
            }, {
                required: true
            });

            const cardHolderNameElement = collectContainer.create({
                table: "credit_cards",
                column: "cardholder_name",
                ...collectStylesOptions,
                label: "Card Holder Name",
                placeholder: "Cardholder Name",
                type: Skyflow.ElementType.CARDHOLDER_NAME,
            }, {
                required: true
            });

            // mount the elements
            cardNumberElement.mount("#collectCardNumber");
            // cvvElement.mount("#collectCvv");
            expiryDateElement.mount("#collectExpiryDate");
            cardHolderNameElement.mount("#collectCardholderName");

            // add listeners to Collect Elements

            // add READY EVENT Listener
            cardNumberElement.on(Skyflow.EventName.READY, (readyState) => {
                console.log("Ready Event Triggered", readyState);
            });

            // add CHANGE EVENT Listener for card number to detect card brand
            cardNumberElement.on(Skyflow.EventName.CHANGE, (changeState) => {
                console.log("CHANGE Event Triggered for Card Number", changeState);

                // Log the entire changeState object to see its structure
                console.log("Full changeState object:", JSON.stringify(changeState));

                // In DEV environment, we can access the actual value and detect card brand
                if (changeState.value && changeState.value.length >= 4) {
                    // Get card brand from the state - check all possible properties
                    let cardBrand = null;

                    // Try different possible properties where card type might be stored
                    if (changeState.cardType) {
                        cardBrand = changeState.cardType;
                    } else if (changeState.card && changeState.card.cardType) {
                        cardBrand = changeState.card.cardType;
                    } else if (changeState.card && changeState.card.brand) {
                        cardBrand = changeState.card.brand;
                    } else if (changeState.brand) {
                        cardBrand = changeState.brand;
                    }

                    // If we found a card brand, display it
                    if (cardBrand) {
                        console.log("Card Brand Detected:", cardBrand);
                        // Map Skyflow card type to user-friendly name
                        let brandName = "";
                        // Convert to uppercase for case-insensitive comparison
                        const brandUpper = cardBrand.toUpperCase();
                        switch (brandUpper) {
                            case "VISA":
                                brandName = "Visa";
                                break;
                            case "MASTERCARD":
                                brandName = "Mastercard";
                                break;
                            case "AMEX":
                            case "AMERICAN EXPRESS":
                                brandName = "American Express";
                                break;
                            case "DINERS_CLUB":
                            case "DINERS CLUB":
                                brandName = "Diners Club";
                                break;
                            case "DISCOVER":
                                brandName = "Discover";
                                break;
                            case "JCB":
                                brandName = "JCB";
                                break;
                            case "MAESTRO":
                                brandName = "Maestro";
                                break;
                            case "UNIONPAY":
                            case "UNION PAY":
                                brandName = "UnionPay";
                                break;
                            case "HIPERCARD":
                                brandName = "Hipercard";
                                break;
                            case "CARTES_BANCAIRES":
                            case "CARTES BANCAIRES":
                                brandName = "Cartes Bancaires";
                                break;
                            default:
                                brandName = cardBrand; // Use the original value if not recognized
                        }
                        // Store the detected brand name in our variable
                        detectedCardBrand = brandName;
                        // Show card brand in alert
                        // alert("This is a " + brandName + " card");
                    } else {
                        // If we couldn't detect the card brand from the changeState object,
                        // try to determine it from the card number prefix
                        const cardNumber = changeState.value;
                        let detectedBrand = "Unknown";

                        // Simple prefix-based detection
                        if (/^4/.test(cardNumber)) {
                            detectedBrand = "Visa";
                        } else if (/^5[1-5]/.test(cardNumber)) {
                            detectedBrand = "Mastercard";
                        } else if (/^3[47]/.test(cardNumber)) {
                            detectedBrand = "American Express";
                        } else if (/^6(?:011|5)/.test(cardNumber)) {
                            detectedBrand = "Discover";
                        } else if (/^(?:2131|1800|35)/.test(cardNumber)) {
                            detectedBrand = "JCB";
                        } else if (/^3(?:0[0-5]|[68])/.test(cardNumber)) {
                            detectedBrand = "Diners Club";
                        } else if (/^62/.test(cardNumber)) {
                            detectedBrand = "UnionPay";
                        }

                        if (detectedBrand !== "Unknown") {
                            // Store the detected brand name in our variable
                            detectedCardBrand = detectedBrand;
                        }
                    }
                }
            });
            // add CHANGE EVENT Listener for card number to detect card brand end

            // add FOCUS EVENT Listener
            expiryDateElement.on(Skyflow.EventName.FOCUS, (focusState) => {
                console.log("FOCUS Event Triggered", focusState);
            });

            // add BLUR EVENT Listener
            cardHolderNameElement.on(Skyflow.EventName.BLUR, (blurState) => {
                console.log("BLUR Event Triggered", blurState);
            });

            // Variable to store the detected card brand
            let detectedCardBrand = "";
            // collect all elements data
            const collectButton = document.getElementById("collectPCIData");
            if (collectButton) {
                collectButton.addEventListener("click", () => {
                    // loader
                    $("#collectPCIData").html(`
                    <div class="spinner-border text-secondary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>`);
                    const collectResponse = collectContainer.collect({
                        additionalFields: {
                            records: [{
                                table: "credit_cards",
                                fields: {
                                    user_id: {{ auth()->id() }},
                                    card_type: detectedCardBrand // Store the detected card brand
                                }
                            }]
                        }
                    });
                    // axios start
                    collectResponse
                        .then((response) => {
                            Swal.fire({
                                title: "Success!",
                                text: "Card Added Successfully",
                                icon: "success"
                            }).then(() => {
                                location.reload();
                            });
                        })
                        .catch((err) => {
                            console.log(err);
                        }).then(() => {
                            $("#collectPCIData").html(`Add Card`);
                        });
                });
            }
        } catch (err) {
            console.log(err);
        }
        setTimeout(() => {
            $('#collectPCIData').prop("disabled", false);
        }, 5000);



        $(document).ready(function() {

            var emailAddress = $('.email_default').val();

            $('#profileForm').on('submit', function(e) {
                e.preventDefault();

                var updatedEmailAddress = $('.email_default').val();
                let formData = new FormData(this);
                formData.delete('email');
                $.ajax({
                    url: $(this).attr('action'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    success: function(response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: 'Profile updated successfully!',
                            confirmButtonText: 'OK'
                        });

                        if (emailAddress != updatedEmailAddress) {
                            // $('#change_email').modal('show');
                            $('#change_email #changeEmail').val(updatedEmailAddress);
                            $('#change_email #change-email-btn').click();
                        }
                    },
                    error: function(xhr) {
                        let errors = xhr.responseJSON?.errors || {};
                        $('.text-danger').remove();

                        $.each(errors, function(key, messages) {
                            let inputField = $(`[name="${key}"]`);
                            messages.forEach(function(message) {
                                inputField.after(
                                    `<p class="text-danger mt-1">${message}</p>`
                                );
                            });
                            inputField.addClass('is-invalid');
                        });

                        if (!errors) {
                            Swal.fire({
                                icon: 'error',
                                title: 'Error',
                                text: 'An error occurred. Please try again.',
                                confirmButtonText: 'OK'
                            });
                        }
                    }
                });
            });
        });
    </script>
@endpush
