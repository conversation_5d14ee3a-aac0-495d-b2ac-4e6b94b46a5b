<?php

namespace App\Services;

use App\Models\UserSession;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use <PERSON><PERSON><PERSON>\Agent\Agent;

class SessionTrackingService
{
    protected $agent;

    public function __construct()
    {
        $this->agent = new Agent();
    }

    /**
     * Create a new user session record.
     */
    public function createSession(Request $request, $user)
    {
        try {
            // Get location data
            $locationData = $this->getLocationData($request->ip());
            
            // Get device information
            $deviceInfo = $this->getDeviceInfo($request);

            // Create session record
            $userSession = UserSession::create([
                'user_id' => $user->id,
                'session_id' => session()->getId(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'device_type' => $deviceInfo['device_type'],
                'browser' => $deviceInfo['browser'],
                'platform' => $deviceInfo['platform'],
                'location' => $locationData['location'] ?? null,
                'country' => $locationData['country'] ?? null,
                'region' => $locationData['region'] ?? null,
                'city' => $locationData['city'] ?? null,
                'latitude' => $locationData['latitude'] ?? null,
                'longitude' => $locationData['longitude'] ?? null,
                'login_time' => now(),
                'last_activity' => now(),
                'is_active' => true
            ]);

            // Limit sessions per user (keep only last 10 active sessions)
            $this->limitUserSessions($user->id);

            return $userSession;

        } catch (\Exception $e) {
            // Log error but don't fail the login process
            \Log::error('Session tracking failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get location data from IP address.
     */
    protected function getLocationData($ipAddress)
    {
        try {
            // Skip for local IPs
            if ($this->isLocalIp($ipAddress)) {
                return [
                    'location' => 'Local/Development',
                    'country' => 'Local',
                    'region' => 'Local',
                    'city' => 'Local'
                ];
            }

            $response = Http::timeout(5)->get("http://ipinfo.io/{$ipAddress}/json");
            
            if ($response->successful()) {
                $data = $response->json();
                
                $location = [];
                if (isset($data['city'])) $location[] = $data['city'];
                if (isset($data['region'])) $location[] = $data['region'];
                if (isset($data['country'])) $location[] = $data['country'];
                
                // Parse coordinates if available
                $latitude = null;
                $longitude = null;
                if (isset($data['loc'])) {
                    $coords = explode(',', $data['loc']);
                    if (count($coords) === 2) {
                        $latitude = (float) $coords[0];
                        $longitude = (float) $coords[1];
                    }
                }

                return [
                    'location' => implode(', ', $location),
                    'country' => $data['country'] ?? null,
                    'region' => $data['region'] ?? null,
                    'city' => $data['city'] ?? null,
                    'latitude' => $latitude,
                    'longitude' => $longitude
                ];
            }
        } catch (\Exception $e) {
            \Log::warning('Failed to get location data: ' . $e->getMessage());
        }

        return [];
    }

    /**
     * Get device information from request.
     */
    protected function getDeviceInfo(Request $request)
    {
        $this->agent->setUserAgent($request->userAgent());

        $deviceType = 'desktop';
        if ($this->agent->isMobile()) {
            $deviceType = 'mobile';
        } elseif ($this->agent->isTablet()) {
            $deviceType = 'tablet';
        }

        return [
            'device_type' => $deviceType,
            'browser' => $this->agent->browser() . ' ' . $this->agent->version($this->agent->browser()),
            'platform' => $this->agent->platform() . ' ' . $this->agent->version($this->agent->platform())
        ];
    }

    /**
     * Check if IP is local/private.
     */
    protected function isLocalIp($ip)
    {
        return in_array($ip, ['127.0.0.1', '::1']) || 
               filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
    }

    /**
     * Limit user sessions to maximum 10 active sessions.
     */
    protected function limitUserSessions($userId, $maxSessions = 10)
    {
        $activeSessions = UserSession::where('user_id', $userId)
            ->where('is_active', true)
            ->orderBy('login_time', 'desc')
            ->get();

        if ($activeSessions->count() > $maxSessions) {
            $sessionsToDeactivate = $activeSessions->slice($maxSessions);
            
            foreach ($sessionsToDeactivate as $session) {
                $session->markAsLoggedOut();
            }
        }
    }

    /**
     * Mark session as logged out.
     */
    public function logoutSession($sessionId)
    {
        $session = UserSession::where('session_id', $sessionId)->first();
        
        if ($session) {
            $session->markAsLoggedOut();
        }
    }

    /**
     * Mark all user sessions as logged out except current.
     */
    public function logoutAllOtherSessions($userId, $currentSessionId = null)
    {
        $query = UserSession::where('user_id', $userId)->where('is_active', true);
        
        if ($currentSessionId) {
            $query->where('session_id', '!=', $currentSessionId);
        }
        
        $sessions = $query->get();
        
        foreach ($sessions as $session) {
            $session->markAsLoggedOut();
        }
        
        return $sessions->count();
    }

    /**
     * Update session activity.
     */
    public function updateSessionActivity($sessionId)
    {
        $session = UserSession::where('session_id', $sessionId)->first();
        
        if ($session) {
            $session->updateLastActivity();
        }
    }
}
