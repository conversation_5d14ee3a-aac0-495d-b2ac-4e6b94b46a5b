<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'ip_address',
        'user_agent',
        'device_type',
        'browser',
        'platform',
        'location',
        'country',
        'region',
        'city',
        'latitude',
        'longitude',
        'login_time',
        'logout_time',
        'last_activity',
        'is_active'
    ];

    protected $dates = [
        'login_time',
        'logout_time',
        'last_activity',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'login_time' => 'datetime',
        'logout_time' => 'datetime',
        'last_activity' => 'datetime'
    ];

    /**
     * Get the user that owns the session.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only active sessions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get sessions for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Mark session as inactive (logged out).
     */
    public function markAsLoggedOut()
    {
        $this->update([
            'is_active' => false,
            'logout_time' => now()
        ]);
    }

    /**
     * Update last activity timestamp.
     */
    public function updateLastActivity()
    {
        $this->update(['last_activity' => now()]);
    }

    /**
     * Get formatted login time.
     */
    public function getFormattedLoginTimeAttribute()
    {
        return $this->login_time ? $this->login_time->format('M d, Y H:i:s') : null;
    }

    /**
     * Get formatted logout time.
     */
    public function getFormattedLogoutTimeAttribute()
    {
        return $this->logout_time ? $this->logout_time->format('M d, Y H:i:s') : null;
    }

    /**
     * Get session duration.
     */
    public function getSessionDurationAttribute()
    {
        if (!$this->login_time) {
            return null;
        }

        $endTime = $this->logout_time ?: now();
        return $this->login_time->diffForHumans($endTime, true);
    }

    /**
     * Check if session is current session.
     */
    public function isCurrentSession()
    {
        return $this->session_id === session()->getId();
    }
}
