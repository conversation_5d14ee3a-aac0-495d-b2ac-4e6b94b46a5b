<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use GuzzleHttp\Client;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    public function authenticated(Request $request, $user)
    {
        try{
            // Track user session
            $sessionTrackingService = app(\App\Services\SessionTrackingService::class);
            $sessionTrackingService->createSession($request, $user);

            // Keep existing activity logging
            $ip = $request->ip();
            $client = new Client();
            $response = $client->get("http://ipinfo.io/{$ip}/json");
            if(isset($response)){
                $location = json_decode($response->getBody()->getContents());
                activity($user->name)
                    ->performedOn($user)
                    ->causedBy($user)
                    ->withProperties($location)
                    ->log('LoggedIn');
            }

        }catch(\Exception $e){

        }
        return back();
        // return redirect($this->redirectTo);
    }

    public function logout(Request $request)
    {
        try{
            $user = auth()->user();

            // Track session logout
            $sessionTrackingService = app(\App\Services\SessionTrackingService::class);
            $sessionTrackingService->logoutSession(session()->getId());

            // Keep existing activity logging
            activity($user->name)
                ->performedOn($user)
                ->causedBy($user)
                ->log('LoggedOut');
            $this->guard()->logout();
            $request->session()->invalidate();
            return redirect('/');
        }catch(\Exception $e){
            return redirect('/');
        }
    }
}
