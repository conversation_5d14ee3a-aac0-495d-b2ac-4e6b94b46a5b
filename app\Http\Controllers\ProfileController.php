<?php

namespace App\Http\Controllers;

use App\Country;
use App\Profile;
use App\Services\ProfileService;
use App\User;
use App\Models\UserSession;
use App\Services\SessionTrackingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class ProfileController extends Controller
{
    function profile_update(Request $request)
    {
        // if (auth()->user()->hasRole("user")) {
            $request->validate([
                // "first_name" => "required",
                // "last_name" => "required",
                "phone" => "required",
                "town" => "required",
                "postal" => "required",
            ]);
            $user = User::findOrFail(auth()->id());
            if (auth()->user()->hasRole("user")) {
                $user->first_name = $request->first_name ?? auth()->user()->first_name;
                $user->last_name = $request->last_name?? auth()->user()->first_name;
            }
            $user->phone = $request->phone ?? $user->phone;
            $user->save();

            $profile = Profile::where("user_id", $user->id)->first();
            $profile->city = $request->town ?? $profile->city;
            $profile->postal = $request->postal ?? $profile->postal;
            $profile->save();
            return redirect()->back()->with(["type" => "success", "title" => "Updated", "message" => "Profile updated"]);
        // } catch (\Exception $e) {
        //     return redirect()->back()->with(["type" => "error", "title" => "Error", "message" => $e->getMessage()]);
        // }
        // }
    }

    function profile_img_update(Request $request)
    {
        $request->validate([
            "profile_img" => "required|mimes:jpeg,png,jpg|max:2048"
        ]);

        $user = User::find(auth()->id());
        if ($request->hasFile("profile_img")) {
            $this->deleteImage($user->avator);
            $user->avatar = $this->storeImage("user-profile", $request->profile_img);
            $user->save();
            // return back()->with(["type" => "success", "title" => "Updated", "message" => "Profile updated"]);
            return response()->json([
                'status' => 'success',
                'message' => 'Profile image updated successfully.',
                'image_url' => asset('website/' . $user->avatar)
            ]);
        } else {
            // return back()->with(["type" => "error", "title" => "Error", "message" => "Profile not updated"]);
            return response()->json([
                'status' => 'error',
                'message' => 'No image uploaded.'
            ], 422);
        }
    }

    function update_password(Request $request, ProfileService $profile_service)
    {
        return $profile_service->update_password($request->all());
    }

    /**
     * Logout a specific user session
     */
    public function logout_session(Request $request)
    {
        try {
            $request->validate([
                'session_id' => 'required|string'
            ]);

            $sessionTrackingService = new SessionTrackingService();

            // Find the session
            $session = UserSession::where('session_id', $request->session_id)
                                 ->where('user_id', auth()->id())
                                 ->where('is_active', true)
                                 ->first();

            if (!$session) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Session not found or already logged out.'
                ], 404);
            }

            // Prevent logging out current session
            if ($session->session_id === session()->getId()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot logout current session.'
                ], 400);
            }

            // Logout the session
            $sessionTrackingService->logoutSession($request->session_id);

            return response()->json([
                'status' => 'success',
                'message' => 'Session logged out successfully.'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to logout session. Please try again.'
            ], 500);
        }
    }
}
